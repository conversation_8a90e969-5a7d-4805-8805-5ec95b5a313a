<template>
  <div class="text-animation-container">
    <div class="static-text">{{ $t('index.staticText') }}</div>
    <div class="dynamic-text">
      <span class="changing-text" ref="changingTextRef"></span>
      <span class="cursor" ref="cursorRef">|</span>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed } from "vue"
import { gsap } from "gsap"
import { TextPlugin } from "gsap/TextPlugin"
import { useI18n } from "vue-i18n"

// 注册 TextPlugin
gsap.registerPlugin(TextPlugin)

const { t, locale } = useI18n()
const changingTextRef = ref(null)
const cursorRef = ref(null)

// 根据当前语言获取 words 数组
const words = computed(() => {
  return [
    t('index.textAnima.word1'),
    t('index.textAnima.word2'),
    t('index.textAnima.word3'),
    t('index.textAnima.word4')
  ]
})

let currentIndex = 0
let animationTimeline = null

// 打字机效果
const typeWriter = (text, onComplete) => {
  const tl = gsap.timeline()

  // 清空文本
  tl.set(changingTextRef.value, { text: "" })

  // 逐字显示
  tl.to(changingTextRef.value, {
    duration: 0.2,
    text: text,
    ease: "none",
    onUpdate: function () {
      // 确保光标在文字后面
      gsap.set(cursorRef.value, { x: 0 })
    },
  })

  // 等待一段时间
  tl.to({}, { duration: 1.5 })

  // 删除文字
  tl.to(changingTextRef.value, {
    duration: 0.05,
    text: "",
    ease: "none",
  })

  // 完成回调
  tl.call(onComplete)

  return tl
}

// 开始动画循环
const startAnimation = () => {
  const animate = () => {
    const currentWord = words.value[currentIndex]

    animationTimeline = typeWriter(currentWord, () => {
      currentIndex = (currentIndex + 1) % words.value.length
      animate()
    })
  }

  animate()
}

// 光标闪烁动画
const startCursorBlink = () => {
  gsap.to(cursorRef.value, {
    opacity: 0,
    duration: 0.5,
    repeat: -1,
    yoyo: true,
    ease: "power2.inOut",
  })
}

onMounted(() => {
  // 开始光标闪烁
  startCursorBlink()

  // 延迟开始打字机效果
  setTimeout(() => {
    startAnimation()
  }, 500)
})

onUnmounted(() => {
  // 清理动画
  if (animationTimeline) {
    animationTimeline.kill()
  }
  gsap.killTweensOf(cursorRef.value)
})
</script>

<style scoped>
.text-animation-container {
  display: flex;
  align-items: center;
  font-size: 2.5rem;
  font-weight: bold;
  color: var(--main-color);
}

.static-text {
  color: var(--main-color);
  margin-right: 8px;
}

.dynamic-text {
  display: flex;
  align-items: center;
  min-width: 120px;
}

.changing-text {
  color: var(--ant-color-error);
  font-weight: 600;
  min-width: 60px;
  display: inline-block;
}

.cursor {
  color: var(--ant-color-error);
  font-weight: bold;
  margin-left: 2px;
  animation: blink 1s infinite;
}

@keyframes blink {
  0%,
  50% {
    opacity: 1;
  }
  51%,
  100% {
    opacity: 0;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .text-animation-container {
    font-size: 1.8rem;
    flex-direction: column;
    gap: 4px;
  }

  .dynamic-text {
    min-width: 80px;
  }

  .changing-text {
    min-width: 40px;
  }
}
</style>
