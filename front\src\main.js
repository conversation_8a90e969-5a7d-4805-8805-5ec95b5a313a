import { createApp } from "vue"
import { createPinia } from "pinia"
import Antd from "ant-design-vue"
import "./style.scss"
import "ant-design-vue/dist/reset.css"
import App from "./App.vue"
import router from "./router"
import i18n from "./i18n"

// 导入主题样式
import './styles/variables.css'
import { initTheme } from './styles/theme'

// 初始化主题
initTheme()

const app = createApp(App)

app.use(createPinia())
app.use(router)
app.use(Antd)
app.use(i18n)

app.mount("#app")
