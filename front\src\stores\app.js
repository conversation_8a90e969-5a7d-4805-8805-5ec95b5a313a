import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useAppStore = defineStore('app', () => {
  // 当前语言
  const locale = ref('zh-CN')
  
  // 用户信息
  const user = ref(null)
  
  // 系统设置
  const settings = ref({
    theme: 'light',
    autoSave: true,
    uploadPath: '/uploads'
  })
  
  // 切换语言
  const setLocale = (newLocale) => {
    locale.value = newLocale
  }
  
  // 设置用户信息
  const setUser = (userInfo) => {
    user.value = userInfo
  }
  
  // 更新设置
  const updateSettings = (newSettings) => {
    settings.value = { ...settings.value, ...newSettings }
  }
  
  return {
    locale,
    user,
    settings,
    setLocale,
    setUser,
    updateSettings
  }
})