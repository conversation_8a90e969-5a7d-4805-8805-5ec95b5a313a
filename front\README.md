# 静态图片管理系统 - 前端

这是一个基于 Vue 3 + Ant Design Vue 的静态图片管理系统前端项目。

## 技术栈

- **Vue 3** - 渐进式 JavaScript 框架
- **Vue Router 4** - 官方路由管理器
- **Pinia** - Vue 状态管理库
- **Ant Design Vue 4** - 企业级 UI 设计语言和 React 组件库
- **Vue I18n** - 国际化插件
- **Vite** - 下一代前端构建工具

## 功能特性

- 🌍 **国际化支持** - 支持中文和英文切换
- 📤 **图片上传** - 拖拽上传，支持多文件
- 🖼️ **图片库管理** - 图片预览、搜索、排序
- 📱 **响应式设计** - 适配各种设备
- 🎨 **现代化UI** - 基于 Ant Design Vue 的美观界面

## 项目结构

```
front/
├── src/
│   ├── components/     # 公共组件
│   ├── views/         # 页面组件
│   │   ├── Home.vue   # 首页
│   │   ├── Upload.vue # 上传页面
│   │   └── Gallery.vue # 图片库页面
│   ├── router/        # 路由配置
│   ├── i18n/          # 国际化配置
│   ├── stores/        # 状态管理
│   ├── utils/         # 工具函数
│   ├── App.vue        # 根组件
│   └── main.js        # 入口文件
├── public/            # 静态资源
├── package.json       # 项目配置
├── vite.config.js     # Vite 配置
└── README.md          # 项目说明
```

## 快速开始

### 安装依赖

```bash
cd front
npm install
```

### 开发环境运行

```bash
npm run dev
```

访问 http://localhost:3000

### 构建生产版本

```bash
npm run build
```

### 代码检查

```bash
npm run lint
```

## 页面说明

### 首页 (Home)
- 系统欢迎页面
- 快速导航到上传和图片库功能
- 语言切换功能

### 上传页面 (Upload)
- 支持拖拽上传图片
- 文件类型和大小验证
- 上传进度显示
- 批量上传支持

### 图片库页面 (Gallery)
- 图片网格展示
- 搜索和筛选功能
- 图片预览和下载
- 复制图片链接
- 删除图片功能

## 国际化

项目支持中文和英文两种语言，可以通过以下方式切换：

1. 在页面右上角选择语言
2. 通过代码设置：`locale.value = 'zh-CN'` 或 `locale.value = 'en-US'`

## API 接口

项目配置了代理，API 请求会转发到后端服务器：

- 开发环境：`http://localhost:8080`
- 生产环境：根据实际部署情况配置

## 开发规范

- 使用 ESLint + Prettier 进行代码格式化
- 组件使用 Composition API 语法
- 样式使用 scoped CSS
- 图标使用 Ant Design Vue 图标库

## 浏览器支持

- Chrome >= 87
- Firefox >= 78
- Safari >= 14
- Edge >= 88

## 许可证

MIT License