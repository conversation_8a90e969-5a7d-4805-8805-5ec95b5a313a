<template>
  <div class="nav">
    <!-- Icon -->
    <div class="icon">
      <img src="@/static/logoNew.png" alt="" />
    </div>

    <!-- Util -->
    <div class="util">
      <a-button type="text">{{ $t("common.about") }}</a-button>
      <a-button type="text">{{ $t("common.contact") }}</a-button>
    </div>

    <!-- Profile -->
    <div class="profile">
      <ThemeSwitch />
      <LangSwitch />
    </div>
  </div>
</template>

<script setup>
import ThemeSwitch from "@/components/ThemeSwitch/ThemeSwitch.vue"
import Button from "@/components/Button/Button.vue"
import LangSwitch from "@/components/LangSwitch/LangSwitch.vue"
import { StepBackwardOutlined } from "@ant-design/icons-vue"
</script>

<style lang="scss" scoped>
/* 深色主题下的毛玻璃效果 */
:root[data-theme="dark"] .nav {
  background-color: var(--main-bg-rgba);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.nav {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 50px;
  background-color: var(--main-bg-rgba);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 200px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.icon {
  width: 50px;
  height: 30px;
  background-color: var(--color-primary);
  border-radius: 50%;
  img {
    width: 100%;
    height: 100%;
  }
}

.util {
  display: flex;
  align-items: center;
  gap: 16px;
}

.profile {
  display: flex;
  align-items: center;
  gap: 16px;
}

.language-display {
  display: flex;
  align-items: center;
  padding: 4px 8px;
  border-radius: 4px;
  background-color: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.current-lang {
  font-size: 12px;
  font-weight: 500;
  color: var(--color-text);
  white-space: nowrap;
}

/* 深色主题下的语言显示样式 */
:root[data-theme="dark"] .language-display {
  background-color: rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
}
</style>
