// 主题切换逻辑 - 颜色值统一在 variables.css 中管理
export const themes = {
  // 默认主题 - 浅色
  default: {
    name: "默认主题",
  },

  // 深色主题
  dark: {
    name: "深色主题",
  },
}

// 获取当前主题
export const getCurrentTheme = () => {
  const savedTheme = localStorage.getItem("theme") || "default"
  return themes[savedTheme] || themes.default
}

// 设置主题
export const setTheme = themeName => {
  if (themes[themeName]) {
    localStorage.setItem("theme", themeName)
    applyTheme(themeName)
  }
}

// 应用主题到CSS变量（带动画）
export const applyTheme = (themeName) => {
  const root = document.documentElement

  // 添加过渡动画类
  root.classList.add("theme-transitioning")

  // 设置data-theme属性 - CSS会自动应用对应的颜色
  root.setAttribute("data-theme", themeName)

  // 延迟移除过渡类，确保动画完成
  setTimeout(() => {
    root.classList.remove("theme-transitioning")
  }, 300)
}

// 初始化主题
export const initTheme = () => {
  const savedTheme = localStorage.getItem("theme") || "default"

  // 初始化时不添加动画
  const root = document.documentElement
  root.classList.add("no-transition")

  applyTheme(savedTheme)

  // 移除无动画类
  setTimeout(() => {
    root.classList.remove("no-transition")
  }, 100)
}

// 获取主题列表
export const getThemeList = () => {
  return Object.entries(themes).map(([key, theme]) => ({
    key,
    name: theme.name,
  }))
}
