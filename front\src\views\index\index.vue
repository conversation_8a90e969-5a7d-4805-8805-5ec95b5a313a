<template>
  <div>
    <Nav />
    <div class="content">
      <TextAnima />
    </div>

    <div class="menus">
      <div class="menus-upload">
        <div class="menus-upload-title">
          <div class="menus-upload-title--text">
            {{ $t("index.menus.title") }}
          </div>
          <div class="menus-upload-title--subtext">
            {{ $t("index.menus.subtext") }}
          </div>
        </div>
        <div class="menus-upload-ctx">
          <div class="menus-upload-ctx--text">在此处投放图片</div>
          <div class="menus-upload-ctx--icon">
            <UploadOutlined style="font-size: 28px" />
          </div>
        </div>
        <div class="menus-upload-btn">
          <Button text="上传图片" />
        </div>
      </div>
      <div class="menus-func"></div>
    </div>
  </div>
</template>

<script setup>
import { ref } from "vue"
import Nav from "@/components/Nav/Nav.vue"
import TextAnima from "@/components/TextAnima/TextAnima.vue"
import TurnBall from "@/components/TurnBall/TurnBall.vue"
import Button from "@/components/Button/Button.vue"
import { UploadOutlined } from "@ant-design/icons-vue"
</script>

<style lang="scss" scoped>
.content {
  padding: 120px 44px 48px;
}

.menus {
  padding: 0 44px;
  .menus-upload {
    width: max-content;
    background: var(--main-sub-bg);
    box-shadow: var(--main-shadow);
    color: var(--main-color);
    border-radius: 12px;
    padding: 12px;
    .menus-upload-title {
      &--text {
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 4px;
      }
      &--subtext {
        font-size: 14px;
        font-weight: 400;
      }
    }
    .menus-upload-ctx {
      margin: 16px 0;
      min-width: 400px;
      border: 2px dashed var(--line-color);
      border-radius: 8px;
      padding: 32px 24px;
      text-align: center;
      background: var(--main-sub-bg);
      transition: all 0.3s ease;
      cursor: pointer;
      position: relative;
      overflow: hidden;

      &:hover {
        border-color: var(--main-color);
        background: var(--main-bg);
        transform: translateY(-2px);
        box-shadow: var(--main-shadow);
      }

      &:active {
        transform: translateY(0);
      }

      &--text {
        font-size: 14px;
        color: var(--main-color);
        margin-bottom: 12px;
        font-weight: 500;
      }

      &--icon {
        color: var(--main-color);
        transition: all 0.3s ease;
      }

      &:hover &--icon {
        color: var(--ant-color-primary);
        transform: scale(1.1);
      }

      // 添加拖拽效果
      &.drag-over {
        border-color: var(--ant-color-primary);
        background: var(--main-bg);
        transform: scale(1.02);
      }

      // 添加文件拖拽时的视觉反馈
      &::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: var(--ant-color-primary);
        opacity: 0;
        transition: opacity 0.3s ease;
        pointer-events: none;
      }

      &.drag-over::before {
        opacity: 0.1;
      }
    }
    .menus-upload-btn {
      margin-top: 16px;
      text-align: center;

      .ant-btn {
        background: var(--ant-color-primary);
        border-color: var(--ant-color-primary);
        color: white;
        border-radius: 6px;
        font-weight: 500;
        transition: all 0.3s ease;

        &:hover {
          background: var(--ant-color-primary);
          border-color: var(--ant-color-primary);
          transform: translateY(-1px);
          box-shadow: var(--main-shadow);
        }

        &:active {
          transform: translateY(0);
        }
      }
    }
  }
  .menus-func {
  }
}
</style>
