import { createRouter, createWebHistory } from 'vue-router'
import index from '@/views/index/index.vue'

const routes = [
  {
    path: '/',
    name: 'index',
    component: index,
    meta: {
      title: '首页',
    },
  },
]

const router = createRouter({
  history: createWebHistory(),
  routes,
})

router.beforeEach((to, from, next) => {
  document.title = to.meta.title || '静态图片管理系统'
  next()
})

export default router
