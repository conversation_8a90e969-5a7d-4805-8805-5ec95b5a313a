import { ref, computed, watch, readonly } from 'vue'
import { themes, getCurrentTheme, setTheme, getThemeList } from '@/styles/theme'

// 当前主题状态
const currentTheme = ref('default')

// 主题列表
const themeList = ref([])

// 当前主题配置
const currentThemeConfig = computed(() => {
  return themes[currentTheme.value] || themes.default
})

// 初始化主题
const initTheme = () => {
  const savedTheme = localStorage.getItem('theme') || 'default'
  currentTheme.value = savedTheme
  themeList.value = getThemeList()
  
  // 应用主题
  const theme = getCurrentTheme()
  setTheme(currentTheme.value)
}

// 切换主题
const changeTheme = (themeName) => {
  if (themes[themeName]) {
    currentTheme.value = themeName
    setTheme(themeName)
  }
}

// 获取主题色彩
const getThemeColor = (colorName) => {
  return currentThemeConfig.value.colors[colorName] || ''
}

// 监听主题变化
watch(currentTheme, (newTheme) => {
  localStorage.setItem('theme', newTheme)
})

export function useTheme() {
  return {
    // 状态
    currentTheme: readonly(currentTheme),
    themeList: readonly(themeList),
    currentThemeConfig: readonly(currentThemeConfig),
    
    // 方法
    initTheme,
    changeTheme,
    getThemeColor,
    
    // 计算属性
    isDarkTheme: computed(() => currentTheme.value === 'dark'),
    primaryColor: computed(() => currentThemeConfig.value.colors.primary)
  }
}