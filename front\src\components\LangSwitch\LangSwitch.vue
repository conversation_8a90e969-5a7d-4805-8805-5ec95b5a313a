<template>
  <div class="toggle-button-cover">
    <div class="button r" id="button-3">
      <input 
        type="checkbox" 
        class="checkbox" 
        :checked="isEnglish"
        @change="handleLanguageToggle"
      />
      <div class="knobs"></div>
      <div class="layer"></div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'

const { locale } = useI18n()

// 当前语言状态
const isEnglish = computed(() => {
  return locale.value === 'en'
})

// 处理语言切换
const handleLanguageToggle = (event) => {
  const isChecked = event.target.checked
  const newLocale = isChecked ? 'en' : 'zh'
  
  // 切换语言
  locale.value = newLocale
  
  // 保存到本地存储
  localStorage.setItem('locale', newLocale)
  
  // 添加切换动画效果
  const buttonElement = event.target.closest('.button')
  buttonElement.classList.add('switching')
  
  // 移除切换动画类
  setTimeout(() => {
    buttonElement.classList.remove('switching')
  }, 300)
}

// 初始化语言
onMounted(() => {
  const savedLocale = localStorage.getItem('locale') || 'zh'
  locale.value = savedLocale
})
</script>

<style scoped>
.toggle-button-cover {
  display: table-cell;
  position: relative;
  width: 80px;
  height: 40px;
  box-sizing: border-box;
}

.button-cover {
  height: 100px;
  margin: 20px;
  background-color: #fff;
  box-shadow: 0 10px 20px -8px #c5d6d6;
  border-radius: 4px;
}

.button-cover:before {
  counter-increment: button-counter;
  content: counter(button-counter);
  position: absolute;
  right: 0;
  bottom: 0;
  color: #d7e3e3;
  font-size: 12px;
  line-height: 1;
  padding: 5px;
}

.button-cover,
.knobs,
.layer {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}

.button {
  position: relative;
  top: 50%;
  width: 50px;
  height: 24px;
  margin: -12px auto 0 auto;
  overflow: hidden;
}

.checkbox {
  position: relative;
  width: 100%;
  height: 100%;
  padding: 0;
  margin: 0;
  opacity: 0;
  cursor: pointer;
  z-index: 3;
}

.knobs {
  z-index: 2;
}

.layer {
  width: 100%;
  background-color: #ebf7fc;
  transition: 0.3s ease all;
  z-index: 1;
}

.button.r,
.button.r .layer {
  border-radius: 100px;
}

#button-3 .knobs:before {
  content: "Zh";
  position: absolute;
  top: 2px;
  left: 2px;
  width: 20px;
  height: 20px;
  color: #fff;
  font-size: 10px;
  font-weight: bold;
  text-align: center;
  line-height: 20px;
  background-color: #1c739b;
  border-radius: 50%;
  transition:
    0.3s ease all,
    left 0.3s cubic-bezier(0.18, 0.89, 0.35, 1.15);
}

#button-3 .checkbox:active + .knobs:before {
  width: 30px;
  border-radius: 100px;
}

#button-3 .checkbox:checked:active + .knobs:before {
  margin-left: -16px;
}

#button-3 .checkbox:checked + .knobs:before {
  content: "En";
  left: 28px;
  background-color: #b6413b;
}

#button-3 .checkbox:checked ~ .layer {
  background-color: #fcebeb;
}

/* 切换动画效果 */
.button.switching {
  animation: switchPulse 0.3s ease-in-out;
}

@keyframes switchPulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

/* 深色主题下的样式调整 */
:root[data-theme="dark"] .layer {
  background-color: #2a2a2a;
}

:root[data-theme="dark"] #button-3 .checkbox:checked ~ .layer {
  background-color: #3a2a2a;
}
</style>
