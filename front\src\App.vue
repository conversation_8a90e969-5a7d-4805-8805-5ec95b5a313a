<template>
  <div id="app">
    <a-config-provider :locale="locale" :theme="themeOpt">
      <router-view />
    </a-config-provider>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from "vue"
import { useI18n } from "vue-i18n"
// 国际化/中文
import zhCN from "ant-design-vue/es/locale/zh_CN"
import enUS from "ant-design-vue/es/locale/en_US"
import dayjs from "dayjs"
import "dayjs/locale/zh-cn"
import "dayjs/locale/en"
dayjs.locale("zh-cn")

const { locale: i18nLocale } = useI18n()

// 根据当前语言设置 Ant Design 的 locale
const locale = computed(() => {
  return i18nLocale.value === "en" ? enUS : zhCN
})

// 获取当前主题
const getCurrentTheme = () => {
  return localStorage.getItem("theme") || "default"
}

// 动态计算主题配置
const themeOpt = computed(() => {
  return {
    token: {
      colorInfo: "var(--ant-color-info)",
      colorPrimary: "var(--ant-color-primary)",
      colorSuccess: "var(--ant-color-success)",
      colorWarning: "var(--ant-color-warning)",
      colorError: "var(--ant-color-error)",
      colorText: "var(--ant-color-text)",
    },
  }
})

// 监听主题变化
watch(
  () => getCurrentTheme(),
  () => {
    // 强制重新渲染
    const app = document.getElementById("app")
    if (app) {
      app.style.display = "none"
      setTimeout(() => {
        app.style.display = "block"
      }, 10)
    }
  },
  { immediate: true }
)

onMounted(() => {
  // 初始化时设置 dayjs 语言
  const savedLocale = localStorage.getItem("locale") || "zh"
  dayjs.locale(savedLocale === "en" ? "en" : "zh-cn")
})
</script>

<style>
#app {
  font-family:
    "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑",
    Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: var(--main-bg);
  color: var(--color-text);
  transition:
    background-color 0.3s ease,
    color 0.3s ease;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  background-color: var(--main-bg);
  color: var(--color-text);
  transition:
    background-color 0.3s ease,
    color 0.3s ease;
}

/* 确保根元素也使用主题背景 */
html {
  background-color: var(--main-bg);
  transition: background-color 0.3s ease;
}
</style>
