<template>
  <div class="container">
    <label for="switch" class="toggle">
      <input 
        type="checkbox" 
        class="input" 
        id="switch" 
        :checked="isDarkTheme"
        @change="handleThemeToggle"
      />
      <div class="icon icon--moon">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 24 24"
          fill="currentColor"
          width="32"
          height="32"
        >
          <path
            fill-rule="evenodd"
            d="M9.528 1.718a.75.75 0 01.162.819A8.97 8.97 0 009 6a9 9 0 009 9 8.97 8.97 0 003.463-.69.75.75 0 01.981.98 10.503 10.503 0 01-9.694 6.46c-5.799 0-10.5-4.701-10.5-10.5 0-4.368 2.667-8.112 6.46-9.694a.75.75 0 01.818.162z"
            clip-rule="evenodd"
          ></path>
        </svg>
      </div>

      <div class="icon icon--sun">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 24 24"
          fill="currentColor"
          width="32"
          height="32"
        >
          <path
            d="M12 2.25a.75.75 0 01.75.75v2.25a.75.75 0 01-1.5 0V3a.75.75 0 01.75-.75zM7.5 12a4.5 4.5 0 119 0 4.5 4.5 0 01-9 0zM18.894 6.166a.75.75 0 00-1.06-1.06l-1.591 1.59a.75.75 0 101.06 1.061l1.591-1.59zM21.75 12a.75.75 0 01-.75.75h-2.25a.75.75 0 010-1.5H21a.75.75 0 01.75.75zM17.834 18.894a.75.75 0 001.06-1.06l-1.59-1.591a.75.75 0 10-1.061 1.06l1.59 1.591zM12 18a.75.75 0 01.75.75V21a.75.75 0 01-1.5 0v-2.25A.75.75 0 0112 18zM7.758 17.303a.75.75 0 00-1.061-1.06l-1.591 1.59a.75.75 0 001.06 1.061l1.591-1.59zM6 12a.75.75 0 01-.75.75H3a.75.75 0 010-1.5h2.25A.75.75 0 016 12zM6.697 7.757a.75.75 0 001.06-1.06l-1.59-1.591a.75.75 0 00-1.061 1.06l1.59 1.591z"
          ></path>
        </svg>
      </div>
    </label>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from "vue"
import { getThemeList, getCurrentTheme, setTheme } from "@/styles/theme"

const currentTheme = ref("default")

// 判断是否为深色主题
const isDarkTheme = computed(() => {
  return currentTheme.value === "dark"
})

// 处理主题切换
const handleThemeToggle = (event) => {
  const isChecked = event.target.checked
  const newTheme = isChecked ? "dark" : "default"
  
  // 添加切换动画效果
  const toggleElement = event.target.closest('.toggle')
  toggleElement.classList.add('switching')
  
  currentTheme.value = newTheme
  setTheme(newTheme)
  
  // 移除切换动画类
  setTimeout(() => {
    toggleElement.classList.remove('switching')
  }, 300)
}

onMounted(() => {
  const savedTheme = localStorage.getItem("theme") || "default"
  currentTheme.value = savedTheme
})
</script>

<style scoped>
.toggle {
  background-color: #fff;
  width: 34px;
  height: 34px;
  border-radius: 50%;
  display: grid;
  place-items: center;
  cursor: pointer;
  box-shadow: 0 0 10px 2px rgba(0, 0, 0, 0.1);
  line-height: 1;
  transition: all 0.3s ease;
}

.toggle:hover {
  transform: scale(1.05);
  box-shadow: 0 0px 12px rgba(0, 0, 0, 0.25);
}

.input {
  display: none;
}

.icon {
  grid-column: 1 / 1;
  grid-row: 1 / 1;
  transition: transform 500ms;
  line-height: 0.1;
  color: #323232;
  width: 20px;
  height: 20px;
}

.icon svg {
  width: 20px;
  height: 20px;
}

.icon--moon {
  transition-delay: 200ms;
}

.icon--sun {
  transform: scale(0);
}

#switch:checked + .icon--moon {
  transform: rotate(360deg) scale(0);
}

#switch:checked ~ .icon--sun {
  transition-delay: 200ms;
  transform: scale(1) rotate(360deg);
}

/* 切换动画效果 */
.toggle.switching {
  animation: switchPulse 0.3s ease-in-out;
}

@keyframes switchPulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

/* 深色主题下的样式调整 */
:root[data-theme="dark"] .toggle {
  background-color: #1f1f1f;
  box-shadow: 0 0 30px 15px rgba(0, 0, 0, 0.3);
}

:root[data-theme="dark"] .icon {
  color: #ffffff;
}

:root[data-theme="dark"] .toggle:hover {
  box-shadow: 0 0 40px 20px rgba(0, 0, 0, 0.4);
}
</style>
