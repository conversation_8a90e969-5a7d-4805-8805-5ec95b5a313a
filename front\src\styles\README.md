# 主题系统使用指南

## 概述

这是一个基于CSS变量的主题系统，支持多种预设主题和自定义主题。所有色彩都通过CSS变量实现，便于统一管理和自定义。

## 文件结构

```
src/styles/
├── theme.js          # 主题配置文件
├── variables.css     # CSS变量定义
└── README.md        # 使用指南
```

## 预设主题

### 1. 默认主题 (default)
- 主色调：蓝色系 (#1890ff)
- 适合：通用场景

### 2. 绿色主题 (green)
- 主色调：绿色系 (#52c41a)
- 适合：环保、自然相关项目

### 3. 紫色主题 (purple)
- 主色调：紫色系 (#722ed1)
- 适合：创意、艺术相关项目

### 4. 橙色主题 (orange)
- 主色调：橙色系 (#fa8c16)
- 适合：活力、温暖相关项目

### 5. 红色主题 (red)
- 主色调：红色系 (#ff4d4f)
- 适合：警告、错误相关项目

### 6. 深色主题 (dark)
- 主色调：深蓝色 (#177ddc)
- 适合：夜间模式、护眼场景

## 使用方法

### 1. 在组件中使用主题色彩

```vue
<template>
  <div class="my-component">
    <button class="primary-btn">主要按钮</button>
    <div class="card">卡片内容</div>
  </div>
</template>

<style scoped>
.my-component {
  background: var(--color-background);
  color: var(--color-text);
}

.primary-btn {
  background: var(--color-primary);
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: var(--border-radius-md);
}

.card {
  background: var(--color-backgroundSecondary);
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-md);
  box-shadow: var(--shadow-sm);
}
</style>
```

### 2. 使用主题切换组件

```vue
<template>
  <div>
    <ThemeSwitcher />
  </div>
</template>

<script setup>
import ThemeSwitcher from '@/components/ThemeSwitcher/ThemeSwitcher.vue'
</script>
```

### 3. 使用主题Composable

```vue
<template>
  <div>
    <p>当前主题：{{ currentTheme }}</p>
    <button @click="changeTheme('green')">切换到绿色主题</button>
  </div>
</template>

<script setup>
import { useTheme } from '@/composables/useTheme'

const { currentTheme, changeTheme } = useTheme()
</script>
```

## CSS变量列表

### 主色调
- `--color-primary`: 主色调
- `--color-primaryHover`: 主色调悬停状态
- `--color-primaryActive`: 主色调激活状态

### 功能色彩
- `--color-success`: 成功色
- `--color-warning`: 警告色
- `--color-error`: 错误色
- `--color-info`: 信息色

### 文字色彩
- `--color-text`: 主要文字色
- `--color-textSecondary`: 次要文字色

### 边框色彩
- `--color-border`: 边框色

### 背景色彩
- `--color-background`: 主背景色
- `--color-backgroundSecondary`: 次要背景色
- `--color-backgroundTertiary`: 第三级背景色

### 阴影色彩
- `--color-shadow`: 阴影色
- `--color-shadowHover`: 悬停阴影色

### 间距
- `--spacing-xs`: 4px
- `--spacing-sm`: 8px
- `--spacing-md`: 16px
- `--spacing-lg`: 24px
- `--spacing-xl`: 32px
- `--spacing-xxl`: 48px

### 圆角
- `--border-radius-sm`: 4px
- `--border-radius-md`: 6px
- `--border-radius-lg`: 8px
- `--border-radius-xl`: 12px

### 字体大小
- `--font-size-xs`: 12px
- `--font-size-sm`: 14px
- `--font-size-md`: 16px
- `--font-size-lg`: 18px
- `--font-size-xl`: 20px
- `--font-size-xxl`: 24px

### 字体粗细
- `--font-weight-normal`: 400
- `--font-weight-medium`: 500
- `--font-weight-semibold`: 600
- `--font-weight-bold`: 700

### 过渡动画
- `--transition-fast`: 0.2s ease
- `--transition-normal`: 0.3s ease
- `--transition-slow`: 0.5s ease

### 阴影
- `--shadow-sm`: 小阴影
- `--shadow-md`: 中等阴影
- `--shadow-lg`: 大阴影
- `--shadow-xl`: 超大阴影

## 自定义主题

### 1. 添加新主题

在 `theme.js` 中添加新主题配置：

```javascript
// 在 themes 对象中添加
custom: {
  name: '自定义主题',
  colors: {
    primary: '#your-color',
    primaryHover: '#your-hover-color',
    // ... 其他色彩
  }
}
```

### 2. 动态修改主题色彩

```javascript
import { setTheme } from '@/styles/theme'

// 动态修改主题色彩
const customTheme = {
  name: '动态主题',
  colors: {
    primary: '#ff0000',
    // ... 其他色彩
  }
}

setTheme('custom')
```

## 最佳实践

1. **始终使用CSS变量**：不要硬编码颜色值
2. **语义化命名**：使用有意义的变量名
3. **保持一致性**：在整个项目中保持色彩使用的一致性
4. **考虑可访问性**：确保色彩对比度符合WCAG标准
5. **测试不同主题**：确保所有主题下界面都美观可用

## 注意事项

1. 主题切换会自动保存到localStorage
2. 页面刷新后会保持上次选择的主题
3. 所有色彩变化都有平滑过渡动画
4. 深色主题会自动调整所有相关色彩