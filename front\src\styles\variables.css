/* 主题色彩变量 - 统一管理所有主题 */
:root {
  /* ===== 浅色主题变量 ===== */
  --main-bg: #fafafa;
  --main-sub-bg: #fafafa;
  --main-bg-rgba: rgba(254, 255, 254, 0.9);

  /* 字体颜色 */
  --main-color: #161616;

  /* 线条颜色 */
  --line-color: #e5e5e5;

  /* 阴影颜色 */
  --main-shadow: 0 0 10px 0 rgba(243, 18, 96, 0.1);

  /* ant颜色变量 */
  --ant-color-info: #161616;
  --ant-color-primary: #1890ff;
  --ant-color-success: #17c964;
  --ant-color-warning: #f5a524;
  --ant-color-error: #f31260;
  --ant-color-text: #1e1e1e;
}

/* ===== 深色主题样式 ===== */
[data-theme="dark"] {
  /* 主色调 */
  --main-bg: #1d1e1f;
  --main-sub-bg: #252627;
  --main-bg-rgba: rgba(29, 30, 31, 0.9);

  /* 字体颜色 */
  --main-color: #fefefe;

  /* 线条颜色 */
  --line-color: #252627;

  /* 阴影颜色 */
  --main-shadow: 0 0 10px 0 rgba(243, 18, 96, 0.1);

  /* ant颜色变量 */
  --ant-color-info: #fefefe;
  --ant-color-primary: #1890ff;
  --ant-color-success: #17c964;
  --ant-color-warning: #f5a524;
  --ant-color-error: #f31260;
  --ant-color-text: #fefefe;
}

/* 为所有元素添加主题切换动画 */
* {
  transition: var(--theme-transition);
}

/* 排除不需要动画的元素 */
*:not(.no-transition) {
  transition: var(--theme-transition);
}

/* 特定元素的过渡时间调整 */
body {
  transition:
    background-color 0.3s ease,
    color 0.3s ease;
}

/* 按钮和交互元素的过渡 */
button,
a,
input,
textarea,
select {
  transition: all 0.2s ease;
}

/* 卡片和容器的过渡 */
.ant-card,
.ant-layout,
.ant-menu {
  transition: all 0.3s ease;
}
