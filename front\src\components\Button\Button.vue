<template>
  <button class="pay-btn" :style="{ background: props.bgColor, color: props.color }">
    <div class="btn-text">{{ props.text }}</div>
    <div class="icon-container">
      <!-- 图片上传图标 -->
      <svg viewBox="0 0 24 24" class="icon upload-icon">
        <path
          d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"
          fill="currentColor"
        ></path>
      </svg>
      
      <!-- 图片编辑图标 -->
      <svg viewBox="0 0 24 24" class="icon edit-icon">
        <path
          d="M20.71,7.04C21.1,6.65 21.1,6 20.71,5.63L18.37,3.29C18,2.9 17.35,2.9 16.96,3.29L15.12,5.12L18.87,8.87M3,17.25V21H6.75L17.81,9.93L14.06,6.18L3,17.25Z"
          fill="currentColor"
        ></path>
      </svg>
      
      <!-- 图片裁剪图标 -->
      <svg viewBox="0 0 24 24" class="icon crop-icon">
        <path
          d="M7,17V1H9V3H11V1H13V3H15V1H17V3H19V5H17V7H19V9H17V11H19V13H17V15H19V17H17V19H15V17H13V19H11V17H9V19H7V17M9,17H11V15H9V17M13,15H15V13H13V15M9,15V13H11V15H9M13,13V11H15V13H13M9,11V9H11V11H9M13,9V7H15V9H13M9,7V5H11V7H9M13,5V3H15V5H13M9,3V1H11V3H9Z"
          fill="currentColor"
        ></path>
      </svg>

      <!-- 图片处理图标 -->
      <svg viewBox="0 0 24 24" class="icon process-icon default-icon">
        <path
          d="M8.5,13.5L11,16.5L14.5,12L19,18H5M21,19V5C21,3.89 20.1,3 19,3H5A2,2 0 0,0 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19M21,15V19H19V15H21M21,11V13H19V11H21M21,7V9H19V7H21M17,5V7H15V5H17M13,5V7H11V5H13M9,5V7H7V5H9M5,5V7H3V5H5M5,9V11H3V9H5M5,13V15H3V13H5M5,17V19H3V17H5M9,17V19H7V17H9M13,17V19H11V17H13M17,17V19H15V17H17Z"
          fill="currentColor"
        ></path>
      </svg>

      <!-- 完成图标 -->
      <svg viewBox="0 0 24 24" class="icon check-icon">
        <path
          d="M9,16.17L4.83,12L3.41,13.41L9,19L21,7L19.59,5.59L9,16.17Z"
          fill="currentColor"
        ></path>
      </svg>
    </div>
  </button>
</template>

<script setup>
import { ref } from "vue"

const props = defineProps({
  text: {
    type: String,
    required: true,
  },
  bgColor: {
    type: String,
    default: "var(--main-color)",
  },
  color: {
    type: String,
    default: "var(--main-bg)",
  },
})
</script>

<style scoped>
.pay-btn {
  position: relative;
  padding: 12px 24px;
  font-size: 16px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  transition: all 0.3s ease;
  width: 100%;
}

.pay-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.4);
}

.icon-container {
  position: relative;
  width: 24px;
  height: 24px;
}

.icon {
  position: absolute;
  top: 0;
  left: 0;
  width: 24px;
  height: 24px;
  color: #22c55e;
  opacity: 0;
  visibility: hidden;
}

.default-icon {
  opacity: 1;
  visibility: visible;
}

/* Hover animations */
.pay-btn:hover .icon {
  animation: none;
}

.pay-btn:hover .process-icon {
  opacity: 0;
  visibility: hidden;
}

.pay-btn:hover .upload-icon {
  animation: iconRotate 2.5s infinite;
  animation-delay: 0s;
}

.pay-btn:hover .edit-icon {
  animation: iconRotate 2.5s infinite;
  animation-delay: 0.5s;
}

.pay-btn:hover .crop-icon {
  animation: iconRotate 2.5s infinite;
  animation-delay: 1s;
}

.pay-btn:hover .check-icon {
  animation: iconRotate 2.5s infinite;
  animation-delay: 1.5s;
}

/* Active state - show only checkmark */
.pay-btn:active .icon {
  animation: none;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.pay-btn:active .check-icon {
  animation: checkmarkAppear 0.6s ease forwards;
  visibility: visible;
}

.btn-text {
  font-weight: 600;
  font-family:
    system-ui,
    -apple-system,
    sans-serif;
}

@keyframes iconRotate {
  0% {
    opacity: 0;
    visibility: hidden;
    transform: translateY(10px) scale(0.5);
  }
  5% {
    opacity: 1;
    visibility: visible;
    transform: translateY(0) scale(1);
  }
  15% {
    opacity: 1;
    visibility: visible;
    transform: translateY(0) scale(1);
  }
  20% {
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px) scale(0.5);
  }
  100% {
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px) scale(0.5);
  }
}

@keyframes checkmarkAppear {
  0% {
    opacity: 0;
    transform: scale(0.5) rotate(-45deg);
  }
  50% {
    opacity: 0.5;
    transform: scale(1.2) rotate(0deg);
  }
  100% {
    opacity: 1;
    transform: scale(1) rotate(0deg);
  }
}
</style>
