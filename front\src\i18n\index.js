import { createI18n } from "vue-i18n"

// 中文语言包
const zhCN = {
  common: {
    upload: "上传",
    delete: "删除",
    edit: "编辑",
    save: "保存",
    cancel: "取消",
    confirm: "确认",
    loading: "加载中...",
    success: "操作成功",
    error: "操作失败",
    warning: "警告",
    info: "提示",
    about: "关于",
    contact: "联系",
  },
  nav: {
    home: "首页",
  },
  index: {
    staticText: "您今天想要",
    textAnima: {
      word1: "去除水印?",
      word2: "批量处理?",
      word3: "智能抠图?",
      word4: "背景替换?",
    },
    menus: {
      title: "上传图片",
      subtext: "支持jpg、png、webp格式",
      btn: "上传",
    },
  },
}

// 英文语言包
const enUS = {
  common: {
    upload: "Upload",
    delete: "Delete",
    edit: "Edit",
    save: "Save",
    cancel: "Cancel",
    confirm: "Confirm",
    loading: "Loading...",
    success: "Success",
    error: "Error",
    warning: "Warning",
    info: "Info",
    about: "About",
    contact: "Contact",
  },
  nav: {
    home: "Home",
  },
  index: {
    staticText: "You want to",
    textAnima: {
      word1: "Remove watermark?",
      word2: "Batch process?",
      word3: "Smart cutout?",
      word4: "Replace background?",
    },
    menus: {
      title: "Upload image",
      subtext: "Support jpg, png, webp format",
      btn: "Upload",
    },
  },
}

const i18n = createI18n({
  legacy: false,
  locale: "zh",
  fallbackLocale: "en",
  messages: {
    zh: zhCN,
    en: enUS,
  },
})

export default i18n
